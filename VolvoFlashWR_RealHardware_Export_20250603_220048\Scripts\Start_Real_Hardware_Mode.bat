@echo off
echo ========================================
echo VolvoFlashWR Real Hardware Mode
echo ========================================
echo.
echo Starting application for real Vocom hardware testing...
echo.

REM Change to application directory
cd /d "%~dp0..\Application"

REM Set environment variables to use local libraries
set PATH=%CD%\Libraries;%PATH%
set APCI_LIBRARY_PATH=%CD%\Libraries
set USE_LOCAL_LIBRARIES=true

REM Check for critical libraries
echo Checking for critical libraries...
if exist "Libraries\WUDFPuma.dll" (
    echo ✓ WUDFPuma.dll found
) else (
    echo ✗ WUDFPuma.dll missing - CRITICAL
)

if exist "Libraries\apci.dll" (
    echo ✓ apci.dll found
) else (
    echo ✗ apci.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlus.dll" (
    echo ✓ Volvo.ApciPlus.dll found
) else (
    echo ✗ Volvo.ApciPlus.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlusData.dll" (
    echo ✓ Volvo.ApciPlusData.dll found
) else (
    echo ✗ Volvo.ApciPlusData.dll missing - CRITICAL
)

echo.
echo IMPORTANT NOTES:
echo - The application requires a REAL Vocom 1 adapter physically connected
echo - Ensure Vocom drivers are installed on this system
echo - The application will fall back to simulation mode if no real hardware is detected
echo - Check the log files in the Logs folder for detailed diagnostics
echo.

REM Start the application
if exist "VolvoFlashWR.Launcher.exe" (
    echo Starting VolvoFlashWR for real hardware...
    start "" "VolvoFlashWR.Launcher.exe" --mode=normal --hardware=real
    echo Application started successfully
    echo Check the application window for connection status.
    echo.
    echo If the application shows "Dummy mode" or simulation responses:
    echo 1. Ensure a real Vocom 1 adapter is connected via USB
    echo 2. Verify Vocom drivers are properly installed
    echo 3. Check that no other applications are using the Vocom adapter
    echo 4. Review the log files for detailed error information
) else (
    echo ERROR: VolvoFlashWR.Launcher.exe not found
    echo Please ensure the export was completed successfully.
    pause
)

echo.
echo Press any key to exit...
pause >nul
