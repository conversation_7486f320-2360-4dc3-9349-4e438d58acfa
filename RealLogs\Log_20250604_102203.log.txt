Log started at 6/4/2025 10:22:03 AM
2025-06-04 10:22:03.729 [Information] LoggingService: Logging service initialized
2025-06-04 10:22:03.737 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 10:22:03.739 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Config
2025-06-04 10:22:03.739 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 10:22:03.746 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 10:22:03.746 [Information] AppConfigurationService: Default configuration created
2025-06-04 10:22:03.747 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 10:22:03.747 [Information] App: Configuration service initialized successfully
2025-06-04 10:22:03.748 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 10:22:03.748 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 10:22:03.748 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 10:22:03.748 [Information] App: Final useDummyImplementations value: False
2025-06-04 10:22:03.748 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 10:22:03.749 [Information] App: usePatchedImplementation flag is: False
2025-06-04 10:22:03.749 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 10:22:03.749 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries'
2025-06-04 10:22:03.749 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 10:22:03.749 [Information] App: verboseLogging flag is: False
2025-06-04 10:22:03.751 [Information] App: Verifying real hardware requirements...
2025-06-04 10:22:03.751 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-06-04 10:22:03.751 [Information] App: ✓ Found critical library: apci.dll
2025-06-04 10:22:03.751 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-06-04 10:22:03.752 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-06-04 10:22:03.752 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 10:22:03.752 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 10:22:03.753 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Drivers\Vocom\config.json
2025-06-04 10:22:03.753 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-06-04 10:22:03.765 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 10:22:03.766 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 10:22:03.766 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 10:22:03.767 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 10:22:03.800 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 10:22:03.804 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 10:22:03.804 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 10:22:03.804 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 10:22:03.805 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 10:22:03.806 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 10:22:03.814 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 10:22:03.815 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 10:22:03.815 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 10:22:03.816 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 10:22:03.816 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 10:22:03.831 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcr120.dll
2025-06-04 10:22:03.841 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp120.dll
2025-06-04 10:22:03.842 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 10:22:03.862 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\msvcp140.dll
2025-06-04 10:22:03.863 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Libraries\vcruntime140.dll
2025-06-04 10:22:03.863 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 10:22:03.866 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 10:22:03.867 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 10:22:03.869 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 10:22:03.871 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 10:22:03.871 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 10:22:03.871 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 10:22:03.871 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 10:22:03.873 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 10:22:03.874 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 10:22:03.875 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 10:22:03.876 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 10:22:03.922 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 10:22:03.923 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 10:22:03.924 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 10:22:03.925 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 10:22:03.926 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 10:22:03.927 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 10:22:03.928 [Information] VocomService: Initializing Vocom service
2025-06-04 10:22:03.929 [Information] VocomService: Checking if PTT application is running
2025-06-04 10:22:03.940 [Information] VocomService: PTT application is not running
2025-06-04 10:22:03.942 [Information] VocomService: Vocom service initialized successfully
2025-06-04 10:22:03.943 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 10:22:03.943 [Information] App: Initializing Vocom service
2025-06-04 10:22:03.943 [Information] VocomService: Initializing Vocom service
2025-06-04 10:22:03.944 [Information] VocomService: Checking if PTT application is running
2025-06-04 10:22:03.955 [Information] VocomService: PTT application is not running
2025-06-04 10:22:03.955 [Information] VocomService: Vocom service initialized successfully
2025-06-04 10:22:03.957 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:03.966 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:03.992 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:03.996 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:03.996 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 10:22:03.998 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 10:22:03.998 [Information] VocomService: Checking if PTT application is running
2025-06-04 10:22:04.009 [Information] VocomService: PTT application is not running
2025-06-04 10:22:04.010 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 10:22:04.011 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 10:22:04.814 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 10:22:04.814 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 10:22:04.815 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 10:22:04.817 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 10:22:04.819 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:04.820 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 10:22:04.822 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 10:22:04.824 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 10:22:04.824 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 10:22:04.826 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 10:22:04.828 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 10:22:04.839 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 10:22:04.841 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 10:22:04.863 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 10:22:04.869 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 10:22:04.872 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 10:22:04.883 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 10:22:04.883 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 10:22:04.884 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 10:22:04.884 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 10:22:04.884 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 10:22:04.884 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 10:22:04.884 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 10:22:04.885 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 10:22:04.885 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 10:22:04.888 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 10:22:04.888 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 10:22:04.888 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 10:22:04.888 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 10:22:04.889 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 10:22:04.889 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 10:22:04.889 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 10:22:04.889 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 10:22:04.891 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 10:22:04.893 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.894 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.894 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.894 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.895 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.896 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.902 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.903 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.904 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.905 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 10:22:04.906 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 10:22:04.907 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 10:22:04.910 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 10:22:04.912 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.912 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.912 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.913 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.913 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.913 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.914 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.914 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.914 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.914 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.915 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.916 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.922 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.923 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.923 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.923 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.923 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.924 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.924 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.924 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.924 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.924 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.925 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.925 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.930 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.930 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.931 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.931 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.931 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.931 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.931 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.932 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.932 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.932 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.932 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.932 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.938 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.939 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.939 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.939 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.939 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.939 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.939 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.940 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.940 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.940 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.940 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.941 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.946 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.947 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.947 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.947 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.947 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.948 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.948 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.948 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.948 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.948 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.949 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.949 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.955 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.955 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.956 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.956 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.956 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.956 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.956 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.957 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.957 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.957 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.957 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.957 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.963 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.964 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.964 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.964 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.964 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.964 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.965 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.965 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.965 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.965 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.965 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.966 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.971 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.971 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.972 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.972 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.972 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.972 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.972 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.973 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.973 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.973 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.973 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.974 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.979 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.979 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.980 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.980 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.980 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.980 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.980 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.980 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.981 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.981 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.981 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.981 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.987 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.987 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.988 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.988 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.988 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.989 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.989 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.989 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.989 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.990 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.990 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.990 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:04.996 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:04.996 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:04.997 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:04.997 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:04.997 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:04.997 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.997 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.997 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:04.998 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.998 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:04.998 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:04.998 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.004 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.004 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.005 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.005 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.005 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.005 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.005 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.005 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.006 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.006 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.006 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.006 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.012 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.012 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.013 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.013 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.013 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.013 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.013 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.014 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.014 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.014 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.014 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.014 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.020 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.020 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.020 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.021 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.021 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.021 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.021 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.022 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.022 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.022 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.022 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.023 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.028 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.028 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.028 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.029 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.029 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.029 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.029 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.029 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.029 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.030 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.030 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.030 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.036 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.037 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.037 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.037 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.037 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.038 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.038 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.038 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.038 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.038 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.039 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.039 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.045 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.045 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.045 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.046 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.046 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.046 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.046 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.046 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.047 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.047 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.047 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.047 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.053 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.053 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.054 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.054 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.054 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.054 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.054 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.054 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.055 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.055 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.055 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.055 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.061 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.061 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.061 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.062 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.062 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.062 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.062 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.062 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.063 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.063 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.063 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.063 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.069 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.069 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.069 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.070 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.070 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.070 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.070 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.070 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.071 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.071 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.071 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.071 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.077 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 10:22:05.078 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 10:22:05.079 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 10:22:05.080 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 10:22:05.091 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 10:22:05.091 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 10:22:05.092 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 10:22:05.093 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.093 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.094 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.094 [Information] VocomService: Using generic data transfer
2025-06-04 10:22:05.095 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 10:22:05.095 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 10:22:05.096 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.096 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.096 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.097 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 10:22:05.098 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.098 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 10:22:05.099 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 10:22:05.100 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 10:22:05.100 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 10:22:05.111 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 10:22:05.111 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 10:22:05.112 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 10:22:05.123 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 10:22:05.134 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 10:22:05.145 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 10:22:05.156 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 10:22:05.166 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 10:22:05.168 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 10:22:05.168 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 10:22:05.179 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 10:22:05.180 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 10:22:05.181 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 10:22:05.191 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 10:22:05.202 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 10:22:05.213 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 10:22:05.224 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 10:22:05.234 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 10:22:05.245 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 10:22:05.247 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 10:22:05.247 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 10:22:05.258 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 10:22:05.259 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 10:22:05.259 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 10:22:05.260 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 10:22:05.260 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 10:22:05.260 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 10:22:05.260 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 10:22:05.260 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 10:22:05.261 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 10:22:05.261 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 10:22:05.261 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 10:22:05.261 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 10:22:05.261 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 10:22:05.262 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 10:22:05.262 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 10:22:05.262 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 10:22:05.262 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 10:22:05.363 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 10:22:05.364 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 10:22:05.366 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 10:22:05.367 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:05.367 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 10:22:05.367 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 10:22:05.367 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:05.368 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 10:22:05.368 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 10:22:05.368 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:05.368 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 10:22:05.369 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 10:22:05.369 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:05.369 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 10:22:05.369 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 10:22:05.370 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 10:22:05.372 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 10:22:05.373 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 10:22:05.379 [Information] BackupService: Initializing backup service
2025-06-04 10:22:05.380 [Information] BackupService: Backup service initialized successfully
2025-06-04 10:22:05.380 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 10:22:05.380 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 10:22:05.381 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 10:22:05.436 [Information] BackupService: Compressing backup data
2025-06-04 10:22:05.444 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-06-04 10:22:05.444 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 10:22:05.444 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 10:22:05.445 [Information] BackupService: Compressing backup data
2025-06-04 10:22:05.447 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (447 bytes)
2025-06-04 10:22:05.448 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 10:22:05.448 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 10:22:05.448 [Information] BackupService: Compressing backup data
2025-06-04 10:22:05.449 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-06-04 10:22:05.450 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 10:22:05.450 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 10:22:05.450 [Information] BackupService: Compressing backup data
2025-06-04 10:22:05.451 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-06-04 10:22:05.452 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 10:22:05.452 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 10:22:05.452 [Information] BackupService: Compressing backup data
2025-06-04 10:22:05.453 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-06-04 10:22:05.454 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 10:22:05.454 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 10:22:05.454 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 10:22:05.455 [Information] BackupService: Compressing backup data
2025-06-04 10:22:05.456 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-06-04 10:22:05.456 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 10:22:05.456 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 10:22:05.458 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 10:22:05.461 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 10:22:05.463 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 10:22:05.504 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 10:22:05.504 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 10:22:05.505 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 10:22:05.506 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 10:22:05.506 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 10:22:05.507 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 10:22:05.507 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 10:22:05.510 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 10:22:05.510 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 10:22:05.518 [Information] LicensingService: Initializing licensing service
2025-06-04 10:22:05.589 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-06-04 10:22:05.591 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 10:22:05.591 [Information] App: Licensing service initialized successfully
2025-06-04 10:22:05.591 [Information] App: License status: Trial
2025-06-04 10:22:05.591 [Information] App: Trial period: 30 days remaining
2025-06-04 10:22:05.592 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 10:22:05.750 [Information] VocomService: Initializing Vocom service
2025-06-04 10:22:05.751 [Information] VocomService: Checking if PTT application is running
2025-06-04 10:22:05.760 [Information] VocomService: PTT application is not running
2025-06-04 10:22:05.761 [Information] VocomService: Vocom service initialized successfully
2025-06-04 10:22:05.812 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 10:22:05.812 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 10:22:05.812 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 10:22:05.813 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 10:22:05.813 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 10:22:05.814 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 10:22:05.814 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 10:22:05.815 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 10:22:05.815 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 10:22:05.815 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 10:22:05.827 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 10:22:05.827 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 10:22:05.827 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 10:22:05.828 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 10:22:05.828 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 10:22:05.828 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 10:22:05.828 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 10:22:05.829 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 10:22:05.829 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 10:22:05.829 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 10:22:05.829 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 10:22:05.829 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 10:22:05.830 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 10:22:05.830 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 10:22:05.830 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 10:22:05.830 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 10:22:05.831 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 10:22:05.831 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 10:22:05.831 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.832 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.832 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.832 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.833 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.833 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.833 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.834 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.834 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.834 [Information] VocomService: Sent 5 bytes and received 8 bytes response
2025-06-04 10:22:05.835 [Error] CANRegisterAccess: Error writing to register 0x0140: Status code 0xAA
2025-06-04 10:22:05.835 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 10:22:05.835 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 10:22:05.836 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.836 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.836 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.836 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.837 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.837 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.837 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.837 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.838 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.838 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.838 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.839 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.844 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.844 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.844 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.845 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.845 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.845 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.845 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.845 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.846 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.846 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.847 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.847 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.853 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.853 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.853 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.853 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.854 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.854 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.854 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.854 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.854 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.855 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.855 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.855 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.862 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.862 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.862 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.862 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.863 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.863 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.863 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.863 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.863 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.864 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.864 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.865 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.870 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.871 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.871 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.871 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.871 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.872 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.872 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.872 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.872 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.872 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.873 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.873 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.879 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.879 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.879 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.879 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.879 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.880 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.880 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.880 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.880 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.881 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.881 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.881 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.887 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.888 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.888 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.888 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.889 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.889 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.889 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.889 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.890 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.890 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.890 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.890 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.897 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.897 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.898 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.898 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.898 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.898 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.899 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.899 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.899 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.900 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.900 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.900 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.906 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.907 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.907 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.907 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.908 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.908 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.908 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.908 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.909 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.909 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.909 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.909 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.915 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.916 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.916 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.916 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.916 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.916 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.917 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.917 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.917 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.917 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.918 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.918 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.923 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.924 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.924 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.924 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.924 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.924 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.925 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.925 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.925 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.925 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.925 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.926 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.931 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.932 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.932 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.932 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.932 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.932 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.932 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.933 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.933 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.933 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.933 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.934 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.939 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.939 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.939 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.940 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.940 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.940 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.941 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.941 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.941 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.941 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.942 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.943 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.949 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.949 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.950 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.960 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.960 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.961 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.961 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.961 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.961 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.962 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.962 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.962 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.968 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.968 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.969 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.969 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.969 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.969 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.969 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.970 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.970 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.970 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.971 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.971 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.978 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.978 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.978 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.979 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.979 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.979 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.979 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.979 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.980 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.980 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.980 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.980 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.987 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.987 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.987 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.987 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.988 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.988 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.988 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.988 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.989 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.989 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.989 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.989 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:05.996 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:05.996 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:05.996 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:05.997 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:05.997 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:05.997 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.997 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.998 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:05.998 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.998 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:05.999 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:05.999 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:06.006 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:06.006 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:06.006 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:06.007 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:06.007 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:06.007 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.007 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.008 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:06.008 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.008 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.009 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:06.009 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:06.016 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 10:22:06.016 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:06.016 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:06.016 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:06.017 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:06.017 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.017 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.017 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:06.018 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.018 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.018 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:06.019 [Error] CANRegisterAccess: Error reading register 0x0141: Status code 0xAA
2025-06-04 10:22:06.025 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 10:22:06.025 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to be active
2025-06-04 10:22:06.026 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 10:22:06.026 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 10:22:06.037 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 10:22:06.037 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 10:22:06.037 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 10:22:06.038 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:06.038 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:06.038 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:06.038 [Information] VocomService: Using generic data transfer
2025-06-04 10:22:06.038 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 10:22:06.038 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 10:22:06.039 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.039 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:06.039 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:06.040 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 10:22:06.040 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:06.040 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 10:22:06.040 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 10:22:06.041 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 10:22:06.041 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 10:22:06.052 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 10:22:06.052 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 10:22:06.052 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 10:22:06.063 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 10:22:06.074 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 10:22:06.085 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 10:22:06.096 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 10:22:06.107 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 10:22:06.108 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 10:22:06.108 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 10:22:06.119 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 10:22:06.120 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 10:22:06.120 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 10:22:06.131 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 10:22:06.142 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 10:22:06.153 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 10:22:06.164 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 10:22:06.176 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 10:22:06.187 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 10:22:06.187 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 10:22:06.187 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 10:22:06.199 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 10:22:06.199 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 10:22:06.199 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 10:22:06.199 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 10:22:06.200 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 10:22:06.200 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 10:22:06.200 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 10:22:06.200 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 10:22:06.200 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 10:22:06.200 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 10:22:06.201 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 10:22:06.201 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 10:22:06.201 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 10:22:06.201 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 10:22:06.201 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 10:22:06.201 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 10:22:06.202 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 10:22:06.302 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 10:22:06.303 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 10:22:06.303 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 10:22:06.304 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:06.304 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 10:22:06.304 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 10:22:06.304 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:06.305 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 10:22:06.305 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 10:22:06.305 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:06.305 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 10:22:06.305 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 10:22:06.306 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 10:22:06.306 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 10:22:06.306 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 10:22:06.357 [Information] BackupService: Initializing backup service
2025-06-04 10:22:06.358 [Information] BackupService: Backup service initialized successfully
2025-06-04 10:22:06.409 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 10:22:06.409 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 10:22:06.411 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Downloads\VolvoFlashWR_RealHardware_Export_20250603_220048\VolvoFlashWR_RealHardware_Export_20250603_220048\Application\Schedules\backup_schedules.json
2025-06-04 10:22:06.411 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 10:22:06.462 [Information] BackupService: Getting predefined backup categories
2025-06-04 10:22:06.513 [Information] MainViewModel: Services initialized successfully
2025-06-04 10:22:06.515 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 10:22:06.516 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:06.517 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:06.517 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:06.518 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:06.520 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 10:22:10.478 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 10:22:10.480 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:10.480 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:10.480 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:10.482 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:10.482 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 10:22:26.088 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 10:22:26.089 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:26.089 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:26.090 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:26.090 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:26.090 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 10:22:37.295 [Information] MainViewModel: Setting operating mode to Open
2025-06-04 10:22:37.297 [Information] ECUCommunicationService: Setting operating mode to Open
2025-06-04 10:22:37.300 [Information] CANProtocolHandler: Setting CAN operating mode to Open
2025-06-04 10:22:37.301 [Information] CANProtocolHandler: Configuring CAN controller for Open mode
2025-06-04 10:22:37.303 [Information] CANProtocolHandler: Sending diagnostic request with ID 0x7E0
2025-06-04 10:22:37.305 [Information] CANProtocolHandler: Waiting for a free CAN transmit buffer
2025-06-04 10:22:37.307 [Information] CANRegisterAccess: Reading byte from register 0x0146
2025-06-04 10:22:37.307 [Information] VocomService: Sending data and waiting for response
2025-06-04 10:22:37.307 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 10:22:37.308 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 10:22:37.308 [Information] VocomService: Detected CAN protocol request
2025-06-04 10:22:37.308 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:37.308 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:37.308 [Information] VocomNativeInterop: Dummy mode: Simulating CAN frame response for device 88890300-BT
2025-06-04 10:22:37.309 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:37.315 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 10:22:37.315 [Information] VocomService: Sent 4 bytes and received 8 bytes response
2025-06-04 10:22:37.315 [Error] CANRegisterAccess: Error reading register 0x0146: Status code 0xAA
2025-06-04 10:22:37.316 [Error] CANProtocolHandler: No free CAN transmit buffers available
2025-06-04 10:22:37.317 [Error] CANProtocolHandler: Failed to send diagnostic request
2025-06-04 10:22:37.318 [Error] CANProtocolHandler: Failed to enter default diagnostic session for Open mode
2025-06-04 10:22:37.318 [Error] ECUCommunicationService: Failed to set operating mode to Open for CAN protocol handler
2025-06-04 10:22:37.320 [Information] SPIProtocolHandler: Setting operating mode to Open via SPI
2025-06-04 10:22:37.321 [Information] SPIProtocolHandler: Configuring SPI controller for Open mode
2025-06-04 10:22:37.321 [Information] SPIProtocolHandler: Writing 0x5C to SPI Control Register 1 (0x00F8)
2025-06-04 10:22:37.321 [Information] SPIProtocolHandler: Writing 0x21 to SPI Baud Rate Register (0x00FA)
2025-06-04 10:22:37.385 [Information] SPIProtocolHandler: Operating mode set to Open via SPI
2025-06-04 10:22:37.386 [Information] SCIProtocolHandler: Setting operating mode to Open via SCI
2025-06-04 10:22:37.387 [Information] SCIProtocolHandler: Configuring SCI controller for Open mode
2025-06-04 10:22:37.496 [Information] SCIProtocolHandler: Operating mode set to Open via SCI
2025-06-04 10:22:37.498 [Information] IICProtocolHandler: Setting operating mode to Open via IIC
2025-06-04 10:22:37.608 [Information] IICProtocolHandler: Operating mode set to Open via IIC
2025-06-04 10:22:37.608 [Error] ECUCommunicationService: Failed to set operating mode to Open for all protocol handlers
2025-06-04 10:22:37.609 [Error] MainViewModel: ECU error: Failed to set operating mode to Open
2025-06-04 10:22:37.610 [Error] MainViewModel: Failed to set operating mode to Open
2025-06-04 10:22:40.908 [Information] MainViewModel: Setting operating mode to Bench
2025-06-04 10:22:40.908 [Information] ECUCommunicationService: Setting operating mode to Bench
2025-06-04 10:22:40.908 [Information] ECUCommunicationService: Operating mode is already set to Bench
2025-06-04 10:22:40.908 [Information] MainViewModel: Operating mode set to Bench
2025-06-04 10:22:49.518 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 10:22:49.519 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:49.519 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:49.519 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:49.520 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:49.521 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 10:22:55.915 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 10:22:55.916 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:55.916 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:55.916 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:55.917 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:55.917 [Information] MainViewModel: Found 2 Vocom device(s)
2025-06-04 10:22:56.142 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 10:22:56.143 [Information] VocomService: Scanning for Vocom devices
2025-06-04 10:22:56.143 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 10:22:56.143 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 10:22:56.144 [Information] VocomService: Found 2 Vocom devices
2025-06-04 10:22:56.144 [Information] MainViewModel: Found 2 Vocom device(s)
